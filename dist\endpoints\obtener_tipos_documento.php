<?php
/**
 * Endpoint para obtener tipos de documentos según el tipo de persona
 * Devuelve los documentos desde la base de datos incluyendo los complementarios
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Solo permitir POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Método no permitido']);
    exit;
}

try {
    // Incluir conexión a la base de datos
    require_once '../con_db.php';
    
    // Obtener datos del request
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['tipo_persona']) || empty($input['tipo_persona'])) {
        throw new Exception('Tipo de persona es requerido');
    }
    
    $tipo_persona = $input['tipo_persona'];
    
    // Validar tipo de persona
    if (!in_array($tipo_persona, ['Natural', 'Juridica'])) {
        throw new Exception('Tipo de persona inválido');
    }
    
    // Consultar tipos de documentos
    $sql = "SELECT
                id,
                codigo,
                nombre,
                descripcion,
                es_obligatorio,
                orden,
                tipo_persona
            FROM tb_inteletgroup_tipos_documento
            WHERE (tipo_persona = ? OR tipo_persona = 'Ambos')
            AND estado = 'Activo'
            ORDER BY
                es_obligatorio DESC,  -- Obligatorios primero
                orden ASC";

    $stmt = $mysqli->prepare($sql);
    if (!$stmt) {
        throw new Exception('Error preparando consulta: ' . $mysqli->error);
    }
    
    $stmt->bind_param("s", $tipo_persona);
    
    if (!$stmt->execute()) {
        throw new Exception('Error ejecutando consulta: ' . $stmt->error);
    }
    
    $result = $stmt->get_result();
    $documentos = [];
    
    while ($row = $result->fetch_assoc()) {
        $documentos[] = [
            'id' => (int)$row['id'],
            'codigo' => $row['codigo'],
            'nombre' => $row['nombre'],
            'descripcion' => $row['descripcion'],
            'es_obligatorio' => (int)$row['es_obligatorio'],
            'orden' => (int)$row['orden'],
            'tipo_persona' => $row['tipo_persona']
        ];
    }
    
    $stmt->close();
    
    // Log para debug
    error_log("Documentos encontrados para $tipo_persona: " . count($documentos));
    error_log("Documentos: " . json_encode($documentos));
    
    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'documentos' => $documentos,
        'total' => count($documentos),
        'obligatorios' => count(array_filter($documentos, function($doc) {
            return $doc['es_obligatorio'] === 1;
        })),
        'opcionales' => count(array_filter($documentos, function($doc) {
            return $doc['es_obligatorio'] === 0;
        }))
    ]);
    
} catch (Exception $e) {
    error_log("Error en obtener_tipos_documento.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}
?>
