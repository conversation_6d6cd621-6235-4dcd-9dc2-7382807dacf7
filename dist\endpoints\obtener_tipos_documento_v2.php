<?php
/**
 * Endpoint para obtener tipos de documentos - Versión simplificada y funcional
 */

// Primero, asegurar que no haya salida antes de los headers
ob_start();

// Headers CORS
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar OPTIONS para CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Solo aceptar POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Método no permitido']);
    exit();
}

try {
    // Obtener datos del request
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['tipo_persona'])) {
        throw new Exception('tipo_persona es requerido');
    }
    
    $tipo_persona = $input['tipo_persona'];
    
    // Validar tipo de persona
    if (!in_array($tipo_persona, ['Natural', 'Juridica'])) {
        throw new Exception('Tipo de persona inválido');
    }
    
    // Incluir conexión a base de datos
    $db_path = dirname(__DIR__) . '/con_db.php';
    if (!file_exists($db_path)) {
        throw new Exception('Archivo de conexión no encontrado');
    }
    
    // Suprimir cualquier salida del archivo de conexión
    ob_start();
    require_once $db_path;
    ob_end_clean();
    
    // Verificar conexión
    if (!isset($conn) && !isset($mysqli)) {
        throw new Exception('No se pudo establecer conexión con la base de datos');
    }
    
    $db = isset($conn) ? $conn : $mysqli;
    
    // Consulta SQL
    $sql = "SELECT 
                id,
                codigo,
                nombre,
                descripcion,
                es_obligatorio,
                orden,
                tipo_persona
            FROM tb_inteletgroup_tipos_documento
            WHERE (tipo_persona = ? OR tipo_persona = 'Ambos')
            AND estado = 'Activo'
            ORDER BY es_obligatorio DESC, orden ASC";
    
    $stmt = $db->prepare($sql);
    if (!$stmt) {
        throw new Exception('Error preparando consulta: ' . $db->error);
    }
    
    $stmt->bind_param("s", $tipo_persona);
    
    if (!$stmt->execute()) {
        throw new Exception('Error ejecutando consulta: ' . $stmt->error);
    }
    
    $result = $stmt->get_result();
    $documentos = [];
    
    while ($row = $result->fetch_assoc()) {
        $documentos[] = [
            'id' => (int)$row['id'],
            'codigo' => $row['codigo'],
            'nombre' => $row['nombre'],
            'descripcion' => $row['descripcion'],
            'es_obligatorio' => (int)$row['es_obligatorio'],
            'orden' => (int)$row['orden'],
            'tipo_persona' => $row['tipo_persona']
        ];
    }
    
    $stmt->close();
    
    // Limpiar cualquier salida anterior
    ob_end_clean();
    
    // Enviar respuesta exitosa
    echo json_encode([
        'success' => true,
        'documentos' => $documentos,
        'total' => count($documentos)
    ]);
    
} catch (Exception $e) {
    // Limpiar buffer si hay error
    ob_end_clean();
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// Cerrar conexión si existe
if (isset($db)) {
    $db->close();
}
?>