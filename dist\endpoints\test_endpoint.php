<?php
/**
 * Script de prueba para verificar el endpoint obtener_tipos_documento.php
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Prueba del endpoint obtener_tipos_documento.php</h2>";

// URL del endpoint
$endpoint_url = 'http://localhost/intranet/dist/endpoints/obtener_tipos_documento.php';

// Datos de prueba
$data = [
    'tipo_persona' => 'Natural'
];

// Configurar la petición cURL
$ch = curl_init($endpoint_url);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

// Ejecutar la petición
echo "<h3>Enviando petición a: $endpoint_url</h3>";
echo "<p>Datos enviados: " . json_encode($data) . "</p>";

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

// Mostrar resultados
echo "<h3>Respuesta:</h3>";
echo "<p>Código HTTP: $httpCode</p>";

if ($error) {
    echo "<p style='color: red;'>Error cURL: $error</p>";
}

echo "<p>Respuesta cruda:</p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

// Decodificar JSON si es posible
$decoded = json_decode($response, true);
if ($decoded !== null) {
    echo "<h3>Respuesta decodificada:</h3>";
    echo "<pre>" . print_r($decoded, true) . "</pre>";
} else {
    echo "<p style='color: red;'>Error al decodificar JSON: " . json_last_error_msg() . "</p>";
}

// Verificar logs
echo "<h3>Últimas líneas del error_log:</h3>";
$log_file = ini_get('error_log');
if ($log_file && file_exists($log_file)) {
    $lines = array_slice(file($log_file), -20);
    echo "<pre>" . htmlspecialchars(implode("", $lines)) . "</pre>";
} else {
    echo "<p>No se pudo leer el archivo de log</p>";
}
?>